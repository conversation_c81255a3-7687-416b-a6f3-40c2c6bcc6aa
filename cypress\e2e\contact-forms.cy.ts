describe('Contact Forms', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  it('opens hero EmailSubscriptionForm', () => {
    cy.get('.grid-in-hero').contains('Chart your concept!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', 'Need a Spark of Inspiration?')
    cy.get('[role="dialog"]').should('contain.text', 'Let me chart my concept!')
    cy.get('[role="dialog"] input[name="name"]').should('exist')
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('exist')
    cy.get('[role="dialog"] input[name="email"]').should('exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('opens hero ContactForm', () => {
    cy.get('.grid-in-hero').contains('Drop a line to start!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")
    cy.get('[role="dialog"]').should('contain.text', 'Submit')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('exist')
    cy.get('[role="dialog"] input[name="name"]').should('not.exist')
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('not.exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('test ContactForm in carousel section overlay', () => {
    cy.get('.grid-in-carousel').contains('Drop a line to start!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")
    cy.get('[role="dialog"]').should('contain.text', 'Submit')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('exist')
    cy.get('[role="dialog"] input[name="name"]').should('not.exist')
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('not.exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('test EmailSubscriptionForm in carousel section overlay', () => {
    cy.get('.grid-in-carousel').contains('Chart your concept!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', 'Need a Spark of Inspiration?')
    cy.get('[role="dialog"]').should('contain.text', 'Let me chart my concept!')
    cy.get('[role="dialog"] input[name="name"]').should('exist')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('not.exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('test ChallengeForm in roadblocks section', () => {
    cy.get('.grid-in-roadblocks').contains('Add my challenge!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', 'We Care About Your Toughest Challenges')
    cy.get('[role="dialog"]').should('contain.text', 'Help me tackle my challenge!')
    cy.get('[role="dialog"] input[name="name"]').should('exist')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="yourChallenge"]').should('exist')
    cy.get('[role="dialog"] input[type="radio"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('not.exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  

  it('opens ContactForm for footer CTA', () => {
    cy.get('footer').contains('Your Story').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")
    cy.get('[role="dialog"]').should('contain.text', 'Submit')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('exist')
    cy.get('[role="dialog"] input[name="name"]').should('not.exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })
})
